package com.ks.app.service.statussaver.ui.videos;

import android.app.ActivityManager;
import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.Toast;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import androidx.media3.exoplayer.ExoPlayer;
import androidx.media3.common.MediaItem;
import androidx.media3.common.Player;
import androidx.media3.ui.PlayerView;
import com.ks.app.service.statussaver.R;

import com.ks.app.service.statussaver.utils.FileUtils;


public class VideoPreviewActivity extends AppCompatActivity {

    private static final String TAG = "VideoPreviewActivity";

    private Uri mediaUri;
    private String fileName;
    private boolean showDownloadButton;
    private ExoPlayer exoPlayer;
    private PlayerView playerView;

    private boolean isLowEndDevice = false;
    private boolean hasBeenPlayed = false; // Track if video has been played once


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_preview);

        // Set status bar color to black for better video viewing
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            getWindow().setStatusBarColor(getResources().getColor(android.R.color.black, getTheme()));
            getWindow().getDecorView().setSystemUiVisibility(View.SYSTEM_UI_FLAG_LAYOUT_STABLE | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
        }

        // ✅ Memory Optimization: Detect low-end devices for optimized video handling
        detectDeviceMemoryClass();

        // Initialize ExoPlayer components
        playerView = findViewById(R.id.video_view_preview);

        // ✅ ENABLE FULL EXOPLAYER CONTROLS
        playerView.setUseController(true);
        playerView.setControllerAutoShow(true);
        playerView.setControllerShowTimeoutMs(5000);
        playerView.setControllerHideOnTouch(true);
        Button buttonDownload = findViewById(R.id.button_download_preview_video);
        Button buttonShare = findViewById(R.id.button_share_preview_video);
        ImageButton buttonBack = findViewById(R.id.button_back);
        TextView textVideoId = findViewById(R.id.text_video_id);

        if (getIntent().getExtras() != null) {
            String uriString = getIntent().getStringExtra("media_uri");
            if (uriString != null) {
                mediaUri = Uri.parse(uriString);
            }
            fileName = getIntent().getStringExtra("file_name");
            showDownloadButton = getIntent().getBooleanExtra("show_download_button", true);
        }

        // Setup back button
        buttonBack.setOnClickListener(v -> finish());

        // Setup video ID display
        if (fileName != null) {
            // Extract video ID from filename (remove extension)
            String videoId = fileName;
            if (videoId.contains(".")) {
                videoId = videoId.substring(0, videoId.lastIndexOf("."));
            }
            textVideoId.setText(videoId);
        } else {
            textVideoId.setText("Video");
        }

        if (mediaUri != null) {
            setupVideoPlayer();
        } else {
            Toast.makeText(this, "Error loading video.", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        if (showDownloadButton) {
            buttonDownload.setVisibility(View.VISIBLE);
            buttonDownload.setOnClickListener(v -> downloadMedia());
        } else {
            buttonDownload.setVisibility(View.GONE);
        }

        buttonShare.setOnClickListener(v -> shareMedia());

        // Optional: Set up system UI for immersive mode
        getWindow().getDecorView().setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION // hide nav bar
            | View.SYSTEM_UI_FLAG_FULLSCREEN // hide status bar
            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY);
    }

    private void setupVideoPlayer() {
        // ✅ Create Optimized ExoPlayer with Memory Management
        exoPlayer = new ExoPlayer.Builder(this).build();

        // Attach player to PlayerView
        playerView.setPlayer(exoPlayer);

        // ✅ Configure ExoPlayer for Memory Efficiency
        playerView.setUseController(true);
        playerView.setControllerAutoShow(false);

        // Create MediaItem from URI
        MediaItem mediaItem = MediaItem.fromUri(mediaUri);
        exoPlayer.setMediaItem(mediaItem);

        // ✅ Setup Player Event Listener for Memory Management
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        Log.d(TAG, "ExoPlayer ready - video prepared successfully");

                        // ✅ Memory Monitoring
                        Log.d(TAG, "Video ready - monitoring memory usage");
                        break;

                    case Player.STATE_ENDED:
                        Log.d(TAG, "Video playback ended - marked as played");
                        // Mark video as played once it ends
                        hasBeenPlayed = true;
                        // Ensure it won't auto-play next time
                        exoPlayer.setPlayWhenReady(false);
                        break;

                    case Player.STATE_BUFFERING:
                        Log.d(TAG, "Video buffering...");
                        break;
                }
            }

            @Override
            public void onIsPlayingChanged(boolean isPlaying) {
                // Only mark as played when video actually starts playing
                if (isPlaying && !hasBeenPlayed) {
                    Log.d(TAG, "Video started playing for the first time");
                    // Don't mark as played immediately, wait for it to end or significant progress
                }
            }
        });

        // Prepare the player
        exoPlayer.prepare();

        // ✅ Auto-play immediately when video is opened (first time)
        // Only prevent auto-play if video has been played before
        if (!hasBeenPlayed) {
            Log.d(TAG, "First time opening video - auto-play immediately");
            exoPlayer.setPlayWhenReady(true); // Auto-play when ready
        } else {
            Log.d(TAG, "Video has been played before - no auto-play, user must click play");
            exoPlayer.setPlayWhenReady(false);
        }

        Log.d(TAG, "ExoPlayer setup completed with full controls");
    }





    private void downloadMedia() {
        if (mediaUri != null && fileName != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                FileUtils.saveStatus(this, mediaUri, fileName, true); // true for isVideo
            } else {
                FileUtils.saveStatusLegacy(this, mediaUri, fileName, true);
            }
        } else {
            Toast.makeText(this, "Cannot download file.", Toast.LENGTH_SHORT).show();
        }
    }

    private void shareMedia() {
        if (mediaUri != null) {
            // Pause ExoPlayer before sharing if it's playing
            if (exoPlayer != null) {
                exoPlayer.pause();
            }
            FileUtils.shareMedia(this, mediaUri);
        } else {
            Toast.makeText(this, "Cannot share file.", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    protected void onPause() {
        super.onPause();
        // ✅ ExoPlayer Memory Optimization: Pause player on activity pause
        if (exoPlayer != null) {
            exoPlayer.pause();
        }

        // ✅ Low-end device optimization: Release video resources immediately on pause
        if (isLowEndDevice) {
            Log.d(TAG, "Low-end device: Releasing ExoPlayer resources on pause");
            releaseVideoPlayer();
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
        // ✅ ExoPlayer Best Practice: Release player resources in onStop()
        releaseVideoPlayer();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // ✅ Recreate player if it was released (low-end devices)
        if (exoPlayer == null && mediaUri != null) {
            Log.d(TAG, "Player was released - recreating with preserved played state");
            setupVideoPlayer();
        }

        // ✅ Auto-play immediately if video hasn't been played before
        // Once a video has been played, user must manually play it again
        if (exoPlayer != null && !hasBeenPlayed) {
            Log.d(TAG, "Video hasn't been played before - auto-play immediately");
            exoPlayer.setPlayWhenReady(true); // Auto-play immediately
        } else if (hasBeenPlayed) {
            Log.d(TAG, "Video has been played before - no auto-play, user must click play");
            // Ensure video doesn't auto-play for previously played videos
            if (exoPlayer != null) {
                exoPlayer.setPlayWhenReady(false);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // ✅ Best Practice 2025: Ensure video memory is released
        releaseVideoPlayer();
    }

    /**
     * ✅ Memory Optimization: Detect device memory class for optimized video handling
     */
    private void detectDeviceMemoryClass() {
        try {
            ActivityManager activityManager = (ActivityManager) getSystemService(Context.ACTIVITY_SERVICE);
            if (activityManager != null) {
                int memoryClass = activityManager.getMemoryClass(); // in MB
                isLowEndDevice = memoryClass <= 128; // Consider devices with 128MB or less as low-end

                Log.d(TAG, "Device memory class: " + memoryClass + "MB, Low-end device: " + isLowEndDevice);

                if (isLowEndDevice) {
                    Log.d(TAG, "Low-end device detected: Enabling memory optimizations");
                }
            }
        } catch (Exception e) {
            Log.w(TAG, "Error detecting device memory class: " + e.getMessage());
            isLowEndDevice = false; // Default to false if detection fails
        }
    }

    /**
     * ✅ Enhanced ExoPlayer Memory Management
     * Releases ExoPlayer resources to prevent memory leaks and OOM errors
     */
    private void releaseVideoPlayer() {
        try {
            // ✅ Release ExoPlayer resources
            if (exoPlayer != null) {
                exoPlayer.release();
                exoPlayer = null;
            }

            // UI state reset - no play button to show

            // ✅ Memory monitoring and cleanup
            Log.d(TAG, "ExoPlayer released successfully");

            // ✅ Low-end device optimization: Force garbage collection
            if (isLowEndDevice) {
                System.gc();
                Log.d(TAG, "Low-end device: Forced garbage collection after ExoPlayer release");
            }

        } catch (Exception e) {
            Log.w(TAG, "Error releasing ExoPlayer: " + e.getMessage());
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        // ✅ Memory Optimization: Release video resources on memory pressure
        if (level >= TRIM_MEMORY_MODERATE) {
            Log.w(TAG, "Memory trim requested (level: " + level + ") - releasing video player");
            releaseVideoPlayer();
        }
    }

    @Override
    public void onLowMemory() {
        super.onLowMemory();
        // ✅ Enhanced Memory Management: Release video memory on low memory warning
        Log.w(TAG, "Low memory warning - releasing video player");
        releaseVideoPlayer();
    }


}