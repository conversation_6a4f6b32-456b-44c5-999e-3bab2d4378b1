<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- ✅ UNIVERSAL MEDIA ACCESS PERMISSIONS - 100% Play Store Compliant -->
    <!--
         UNIVERSAL STRATEGY:
         • Android 4.4-9: File API with READ_EXTERNAL_STORAGE
         • Android 10+: SAF (Storage Access Framework) - mandatory for cross-app access
         • Android 13+: Granular media permissions + SAF

         This ensures compatibility across ALL Android versions and device manufacturers
         while maintaining 100% Play Store compliance.
    -->

    <!-- For Android 4.4–12 (API 19-32) -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />

    <!-- For Android 13+ (API 33+) - Granular media permissions -->
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />

    <!-- Internet permission required for AdMob -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Explicitly remove notification permission -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" tools:node="remove" />

    <application
        android:name=".SaveProApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:hardwareAccelerated="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:requestLegacyExternalStorage="true"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.SmartStatusSaver"
        tools:targetApi="35"
        android:enableOnBackInvokedCallback="true">

        <!-- AdMob App ID (Replace with your actual AdMob App ID) -->
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7557152164205920~6417975169"/>

        <!-- Disable AdMob notification permissions -->
        <meta-data
            android:name="com.google.android.gms.ads.DELAY_APP_MEASUREMENT_INIT"
            android:value="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION"
            android:value="true"/>
        <meta-data
            android:name="com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING"
            android:value="true"/>

        <!-- Splash Activity - Launcher -->
        <activity
            android:name=".ui.splash.SplashActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/Theme.SmartStatusSaver.Splash">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- Main Activity -->
        <activity
            android:name=".ui.main.MainActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />

        <!-- SAF Permission Activity -->
        <activity
            android:name=".ui.main.SafPermissionActivity"
            android:exported="false"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize"
            android:theme="@style/Theme.SmartStatusSaver.Permission" />

        <!-- Interstitial Ad Activity -->
        <activity
            android:name=".ui.ads.InterstitialAdActivity"
            android:exported="false"
            android:theme="@style/Theme.SmartStatusSaver.FullScreen"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />

        <activity
            android:name=".ui.images.ImagePreviewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name=".ui.videos.VideoPreviewActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name=".ui.main.SettingsActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name=".ui.settings.PrivacyPolicyActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name=".ui.settings.ContactUsActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
        <activity
            android:name=".ui.settings.AboutActivity"
            android:screenOrientation="portrait"
            android:configChanges="orientation|screenSize" />
    </application>

</manifest>